"""
APPAFL原始算法主训练文件
使用原APPAFL的客户端训练算法，支持异步联邦学习和延迟处理
"""

import os
import argparse
import random
import numpy as np
import torch
import torch.nn.functional as F
from torch import optim
import time

from models import create_model, CNNCifar, ResNet18_cifar, ResNet50_cifar
from data_utils import create_data_loaders


def test_mkdir(path):
    """创建目录"""
    if not os.path.isdir(path):
        os.mkdir(path)


def test_accuracy(net, testDataLoader, device):
    """测试模型准确率"""
    net.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for data, target in testDataLoader:
            data, target = data.to(device), target.to(device)
            outputs = net(data)
            _, predicted = torch.max(outputs.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
    
    accuracy = 100.0 * correct / total
    return accuracy


def main():
    # 参数设置
    args = {
        'num_of_clients': 100,      # 客户端总数
        'cfraction': 0.1,           # 每轮参与的客户端比例
        'num_comm': 100,            # 通信轮数
        'epoch': 5,                 # 本地训练轮数
        'batchsize': 10,            # 批次大小
        'learning_rate': 0.01,      # 正常客户端学习率
        'delayed_learning_rate': 0.02,  # 延迟客户端学习率
        'IID': False,               # 数据分布（False为Non-IID）
        'mu': 0.01,                 # 正则化参数
        'model_type': 'resnet18',   # 模型类型：'cnn', 'resnet18', 'resnet50'
        'use_project_head': False,  # 是否使用投影头
        'save_path': './checkpoints_appafl_original/',
        'device': 'cuda' if torch.cuda.is_available() else 'cpu'
    }

    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)

    # 创建保存目录
    test_mkdir(args['save_path'])

    # 设备设置
    device = torch.device(args['device'])
    print(f"使用设备: {device}")

    # 创建模型
    if args['model_type'] == 'cnn':
        net = CNNCifar()
    elif args['model_type'] == 'resnet18':
        net = ResNet18_cifar(num_classes=10)
    elif args['model_type'] == 'resnet50':
        net = ResNet50_cifar(num_classes=10)
    else:
        raise ValueError(f"不支持的模型类型: {args['model_type']}")

    net.to(device)
    print(f"创建模型: {args['model_type']}")

    # 损失函数
    loss_func = F.cross_entropy

    # 创建客户端数据
    myClients = create_data_loaders('cifar10', args['IID'], args['num_of_clients'], device, args["mu"])
    testDataLoader = myClients.test_data_loader

    # 计算每轮参与的客户端数量
    num_in_comm = int(max(args['num_of_clients'] * args['cfraction'], 1))

    # 异步联邦学习参数
    client_nums = args['num_of_clients']
    percentageOfStale = 0.4  # 延迟客户端比例
    threshold = 3  # 陈旧模型聚合阈值
    stale_threshold = 6  # 最大延迟轮数

    # 客户端状态跟踪
    client_staleness = [0] * client_nums  # 客户端陈旧程度
    client_last_round = [-1] * client_nums  # 客户端最后参与的轮次

    AsyAccuracy = []  # 存放模型准确率

    # 全局模型参数
    global_parameters = {}
    stale_global_parameters = {}
    for key, var in net.state_dict().items():
        global_parameters[key] = var.clone()
        stale_global_parameters[key] = var.clone()

    # 打印关键参数信息
    print("="*60)
    print("异步联邦学习参数设置:")
    print(f"  总客户端数: {client_nums}")
    print(f"  每轮参与客户端数: {num_in_comm}")
    print(f"  延迟客户端比例: {percentageOfStale}")
    print(f"  正常客户端学习率: {args['learning_rate']}")
    print(f"  延迟客户端学习率: {args['delayed_learning_rate']}")
    print(f"  陈旧模型聚合阈值: {threshold}")
    print(f"  最大延迟轮数: {stale_threshold}")
    print(f"  模型类型: {args['model_type']}")
    print("="*60)

    # 开始联邦学习训练
    for i in range(args['num_comm']):
        print(f"\n=== 通信轮次 {i + 1} ===")
        
        # 随机选择参与的客户端
        order = np.random.permutation(args['num_of_clients'])
        clients_in_comm = ['client{}'.format(j) for j in order[0:num_in_comm]]
        
        sum_parameters = None
        aggregated_clients = []  # 实际参与聚合的客户端
        
        # 确定延迟客户端
        num_delayed = int(num_in_comm * percentageOfStale)
        delayed_clients = np.random.choice(range(num_in_comm), num_delayed, replace=False)
        
        print(f"选中客户端: {[int(c.replace('client', '')) for c in clients_in_comm]}")
        print(f"延迟客户端索引: {delayed_clients}")

        for idx, client in enumerate(clients_in_comm):
            client_id = int(client.replace('client', ''))
            
            # 计算客户端陈旧程度
            if client_last_round[client_id] == -1:
                staleness = 0  # 首次参与
            else:
                staleness = i - client_last_round[client_id]
            
            client_staleness[client_id] = staleness
            
            # 判断是否为延迟客户端
            is_delayed = idx in delayed_clients
            
            if is_delayed and staleness > stale_threshold:
                print(f"客户端 {client_id} 延迟过久 (陈旧程度: {staleness})，跳过聚合")
                continue
            
            # 选择学习率和全局模型
            if staleness <= 1 or i == 0:
                print(f"客户端 {client_id} 使用当前全局模型进行训练")
                opti = optim.SGD(net.parameters(), lr=args['learning_rate'])
                used_global_params = global_parameters
            else:
                print(f"客户端 {client_id} 是延迟客户端，陈旧程度: {staleness}")
                delayed_lr = min(args['delayed_learning_rate'], 0.02)  # 限制最大学习率
                opti = optim.SGD(net.parameters(), lr=delayed_lr)
                used_global_params = stale_global_parameters
            
            # 本地训练
            local_parameters = myClients.clients_set[client].localUpdate(
                args['epoch'], args['batchsize'], net, loss_func, opti, 
                used_global_params, args["mu"]
            )
            
            # 计算聚合权重（基于陈旧程度）
            if staleness <= threshold:
                weight = 1.0 / (1.0 + staleness * 0.1)  # 陈旧程度越高，权重越小
            else:
                weight = 0.5  # 过于陈旧的模型使用较小权重
            
            # 参与聚合
            if sum_parameters is None:
                sum_parameters = {}
                for key, var in local_parameters.items():
                    sum_parameters[key] = var.clone() * weight
                total_weight = weight
            else:
                for key, var in local_parameters.items():
                    sum_parameters[key] += var * weight
                total_weight += weight
            
            aggregated_clients.append((client_id, weight))
            client_last_round[client_id] = i

        # 更新全局模型
        if sum_parameters is not None:
            # 保存当前全局模型作为陈旧模型
            for key, var in global_parameters.items():
                stale_global_parameters[key] = var.clone()
            
            # 更新全局模型
            for key in global_parameters:
                global_parameters[key] = sum_parameters[key] / total_weight
            
            print(f"模型聚合完成，参与客户端: {[f'{cid}(权重:{w:.3f})' for cid, w in aggregated_clients]}")
        else:
            print("警告：没有客户端参与聚合，保持全局模型不变")

        # 测试全局模型准确率
        if (i + 1) % 5 == 0 or i == 0:  # 每5轮测试一次
            net.load_state_dict(global_parameters)
            accuracy = test_accuracy(net, testDataLoader, device)
            AsyAccuracy.append(accuracy)
            print(f"第 {i + 1} 轮全局模型准确率: {accuracy:.2f}%")
            
            # 保存模型
            torch.save(global_parameters, 
                      f"{args['save_path']}/global_model_round_{i+1}.pth")

    # 最终测试
    net.load_state_dict(global_parameters)
    final_accuracy = test_accuracy(net, testDataLoader, device)
    print(f"\n最终全局模型准确率: {final_accuracy:.2f}%")
    
    # 保存最终模型和准确率记录
    torch.save(global_parameters, f"{args['save_path']}/final_global_model.pth")
    np.save(f"{args['save_path']}/accuracy_record.npy", AsyAccuracy)
    
    print(f"训练完成！模型和记录已保存到 {args['save_path']}")


if __name__ == "__main__":
    main()
