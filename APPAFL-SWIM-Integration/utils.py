"""
APPAFL-SWIM整合工具函数模块
包含通用的工具函数，如模型评估、数据处理、可视化等
"""

import os
import logging
import numpy as np
import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix
import seaborn as sns


# 配置日志系统
logging.basicConfig()
logger = logging.getLogger()
logger.setLevel(logging.INFO)


def mkdirs(dirpath):
    """
    创建目录（如果不存在）
    
    Args:
        dirpath: 要创建的目录路径
    """
    try:
        os.makedirs(dirpath, exist_ok=True)
    except Exception as e:
        logger.warning(f"创建目录失败: {e}")


def set_random_seed(seed=42):
    """
    设置随机种子以确保实验可重复性
    
    Args:
        seed: 随机种子值
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    import random
    random.seed(seed)
    
    # 确保CUDA操作的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def compute_accuracy(net, dataloader, device):
    """
    计算模型在数据集上的准确率
    
    Args:
        net: 神经网络模型
        dataloader: 数据加载器
        device: 计算设备
        
    Returns:
        accuracy: 准确率（百分比）
    """
    net.eval()
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in dataloader:
            data, target = data.to(device), target.to(device)
            
            # 根据模型类型进行前向传播
            if hasattr(net, 'l3'):  # SWIM模型
                _, _, outputs = net(data)
            else:  # 普通模型
                outputs = net(data)
                
            _, predicted = torch.max(outputs.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
    
    accuracy = 100.0 * correct / total
    return accuracy


def compute_loss(net, dataloader, device, loss_func=F.cross_entropy):
    """
    计算模型在数据集上的平均损失
    
    Args:
        net: 神经网络模型
        dataloader: 数据加载器
        device: 计算设备
        loss_func: 损失函数
        
    Returns:
        avg_loss: 平均损失
    """
    net.eval()
    total_loss = 0.0
    total_samples = 0
    
    with torch.no_grad():
        for data, target in dataloader:
            data, target = data.to(device), target.to(device)
            
            # 根据模型类型进行前向传播
            if hasattr(net, 'l3'):  # SWIM模型
                _, _, outputs = net(data)
            else:  # 普通模型
                outputs = net(data)
            
            loss = loss_func(outputs, target, reduction='sum')
            total_loss += loss.item()
            total_samples += target.size(0)
    
    avg_loss = total_loss / total_samples
    return avg_loss


def evaluate_model_detailed(net, dataloader, device, class_names=None):
    """
    详细评估模型性能，包括准确率、损失、混淆矩阵等
    
    Args:
        net: 神经网络模型
        dataloader: 数据加载器
        device: 计算设备
        class_names: 类别名称列表
        
    Returns:
        dict: 包含各种评估指标的字典
    """
    net.eval()
    all_predictions = []
    all_targets = []
    total_loss = 0.0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in dataloader:
            data, target = data.to(device), target.to(device)
            
            # 根据模型类型进行前向传播
            if hasattr(net, 'l3'):  # SWIM模型
                _, _, outputs = net(data)
            else:  # 普通模型
                outputs = net(data)
            
            # 计算损失
            loss = F.cross_entropy(outputs, target, reduction='sum')
            total_loss += loss.item()
            
            # 计算预测
            _, predicted = torch.max(outputs.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
            
            # 收集预测和真实标签
            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
    
    # 计算指标
    accuracy = 100.0 * correct / total
    avg_loss = total_loss / total
    
    # 计算混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions)
    
    # 计算每类准确率
    class_accuracies = cm.diagonal() / cm.sum(axis=1)
    
    results = {
        'accuracy': accuracy,
        'avg_loss': avg_loss,
        'confusion_matrix': cm,
        'class_accuracies': class_accuracies,
        'predictions': all_predictions,
        'targets': all_targets
    }
    
    return results


def plot_training_curves(accuracy_records, save_path=None, title="Training Accuracy"):
    """
    绘制训练准确率曲线
    
    Args:
        accuracy_records: 准确率记录列表
        save_path: 保存路径
        title: 图表标题
    """
    plt.figure(figsize=(10, 6))
    plt.plot(accuracy_records, 'b-', linewidth=2, marker='o', markersize=4)
    plt.title(title, fontsize=16)
    plt.xlabel('Communication Round', fontsize=14)
    plt.ylabel('Accuracy (%)', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练曲线已保存到: {save_path}")
    
    plt.show()


def plot_confusion_matrix(cm, class_names=None, save_path=None, title="Confusion Matrix"):
    """
    绘制混淆矩阵
    
    Args:
        cm: 混淆矩阵
        class_names: 类别名称列表
        save_path: 保存路径
        title: 图表标题
    """
    plt.figure(figsize=(10, 8))
    
    if class_names is None:
        class_names = [f'Class {i}' for i in range(cm.shape[0])]
    
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title(title, fontsize=16)
    plt.xlabel('Predicted Label', fontsize=14)
    plt.ylabel('True Label', fontsize=14)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"混淆矩阵已保存到: {save_path}")
    
    plt.show()


def compare_algorithms(appafl_acc, swim_acc, save_path=None):
    """
    比较APPAFL原始算法和SWIM算法的性能
    
    Args:
        appafl_acc: APPAFL准确率记录
        swim_acc: SWIM准确率记录
        save_path: 保存路径
    """
    plt.figure(figsize=(12, 8))
    
    # 确保两个列表长度一致
    min_len = min(len(appafl_acc), len(swim_acc))
    rounds = range(1, min_len + 1)
    
    plt.plot(rounds, appafl_acc[:min_len], 'b-', linewidth=2, marker='o', 
             markersize=4, label='APPAFL Original')
    plt.plot(rounds, swim_acc[:min_len], 'r-', linewidth=2, marker='s', 
             markersize=4, label='APPAFL-SWIM')
    
    plt.title('Algorithm Performance Comparison', fontsize=16)
    plt.xlabel('Communication Round', fontsize=14)
    plt.ylabel('Accuracy (%)', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"算法比较图已保存到: {save_path}")
    
    plt.show()


def save_experiment_results(results_dict, save_path):
    """
    保存实验结果
    
    Args:
        results_dict: 结果字典
        save_path: 保存路径
    """
    mkdirs(os.path.dirname(save_path))
    
    # 保存为numpy格式
    np.savez(save_path, **results_dict)
    print(f"实验结果已保存到: {save_path}")


def load_experiment_results(load_path):
    """
    加载实验结果
    
    Args:
        load_path: 加载路径
        
    Returns:
        dict: 结果字典
    """
    data = np.load(load_path)
    results_dict = {key: data[key] for key in data.files}
    print(f"实验结果已从 {load_path} 加载")
    return results_dict


def print_model_info(model):
    """
    打印模型信息
    
    Args:
        model: PyTorch模型
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print("="*50)
    print("模型信息:")
    print(f"  模型类型: {model.__class__.__name__}")
    print(f"  总参数数量: {total_params:,}")
    print(f"  可训练参数数量: {trainable_params:,}")
    print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
    print("="*50)


def record_client_data_distribution(clients_group, save_path=None):
    """
    记录客户端数据分布情况
    
    Args:
        clients_group: 客户端组对象
        save_path: 保存路径
    """
    distribution_info = {}
    
    for client_id, client in clients_group.clients_set.items():
        # 获取客户端数据标签
        labels = []
        for _, label in client.train_ds:
            labels.append(label.item())
        
        # 统计标签分布
        unique, counts = np.unique(labels, return_counts=True)
        distribution_info[client_id] = dict(zip(unique.tolist(), counts.tolist()))
    
    if save_path:
        import json
        with open(save_path, 'w') as f:
            json.dump(distribution_info, f, indent=2)
        print(f"客户端数据分布已保存到: {save_path}")
    
    return distribution_info


def calculate_data_heterogeneity(distribution_info):
    """
    计算数据异质性指标
    
    Args:
        distribution_info: 客户端数据分布信息
        
    Returns:
        float: 异质性指标（0-1，越大越异质）
    """
    all_distributions = []
    
    for client_id, dist in distribution_info.items():
        # 将分布转换为概率向量
        total_samples = sum(dist.values())
        prob_vector = [dist.get(i, 0) / total_samples for i in range(10)]  # 假设10个类别
        all_distributions.append(prob_vector)
    
    # 计算分布之间的平均KL散度
    from scipy.spatial.distance import jensenshannon
    
    heterogeneity_scores = []
    for i in range(len(all_distributions)):
        for j in range(i + 1, len(all_distributions)):
            js_distance = jensenshannon(all_distributions[i], all_distributions[j])
            heterogeneity_scores.append(js_distance)
    
    avg_heterogeneity = np.mean(heterogeneity_scores)
    return avg_heterogeneity


# CIFAR-10类别名称
CIFAR10_CLASSES = [
    'airplane', 'automobile', 'bird', 'cat', 'deer',
    'dog', 'frog', 'horse', 'ship', 'truck'
]
