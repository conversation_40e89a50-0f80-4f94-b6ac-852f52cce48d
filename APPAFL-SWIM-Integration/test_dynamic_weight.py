#!/usr/bin/env python3
"""
动态权重计算功能测试脚本
测试APPAFL-SWIM融合算法中的动态权重调整功能
"""

import numpy as np
import matplotlib.pyplot as plt
from main_appafl_swim import calculate_async_dynamic_weight

def test_dynamic_weight_strategies():
    """测试不同的动态权重计算策略"""
    print("=" * 60)
    print("测试动态权重计算策略")
    print("=" * 60)
    
    # 测试参数
    total_epochs = 10
    total_rounds = 50
    strategies = ['local_rounds', 'global_progress', 'fixed_schedule', 'exponential_decay', 'original']
    
    # 存储结果用于可视化
    results = {}
    
    for strategy in strategies:
        print(f"\n测试策略: {strategy}")
        print("-" * 40)
        
        weights = []
        rounds = []
        
        # 模拟不同轮次的权重变化
        for round_num in range(0, total_rounds, 5):
            for epoch in range(0, total_epochs, 2):
                local_training_rounds = round_num * total_epochs + epoch
                
                weight = calculate_async_dynamic_weight(
                    strategy=strategy,
                    current_epoch=epoch,
                    total_epochs=total_epochs,
                    round_num=round_num,
                    total_rounds=total_rounds,
                    local_training_rounds=local_training_rounds
                )
                
                weights.append(weight)
                rounds.append(round_num + epoch / total_epochs)
                
                if round_num % 10 == 0 and epoch == 0:
                    print(f"  轮次 {round_num}, Epoch {epoch}: 权重 = {weight:.4f}")
        
        results[strategy] = (rounds, weights)
        print(f"  策略 {strategy} 权重范围: {min(weights):.4f} - {max(weights):.4f}")
    
    return results

def test_async_scenarios():
    """测试异步场景下的权重计算"""
    print("\n" + "=" * 60)
    print("测试异步场景下的权重计算")
    print("=" * 60)
    
    # 模拟不同的异步场景
    scenarios = [
        {
            'name': '正常客户端',
            'round_num': 10,
            'local_rounds': 100,
            'description': '客户端与全局同步'
        },
        {
            'name': '轻微延迟客户端',
            'round_num': 8,  # 延迟2轮
            'local_rounds': 80,
            'description': '客户端延迟2轮'
        },
        {
            'name': '严重延迟客户端',
            'round_num': 5,  # 延迟5轮
            'local_rounds': 50,
            'description': '客户端延迟5轮'
        },
        {
            'name': '快速客户端',
            'round_num': 12,  # 超前2轮
            'local_rounds': 120,
            'description': '客户端训练较快'
        }
    ]
    
    total_epochs = 10
    total_rounds = 50
    current_epoch = 5
    
    print(f"测试条件: 总轮次={total_rounds}, 总Epochs={total_epochs}, 当前Epoch={current_epoch}")
    print()
    
    for scenario in scenarios:
        print(f"场景: {scenario['name']} ({scenario['description']})")
        print("-" * 50)
        
        for strategy in ['local_rounds', 'global_progress', 'fixed_schedule']:
            weight = calculate_async_dynamic_weight(
                strategy=strategy,
                current_epoch=current_epoch,
                total_epochs=total_epochs,
                round_num=scenario['round_num'],
                total_rounds=total_rounds,
                local_training_rounds=scenario['local_rounds']
            )
            print(f"  {strategy:15}: {weight:.4f}")
        print()

def visualize_weight_evolution(results):
    """可视化权重演化过程"""
    print("=" * 60)
    print("生成权重演化可视化图表")
    print("=" * 60)
    
    try:
        plt.figure(figsize=(12, 8))
        
        colors = ['blue', 'red', 'green', 'orange', 'purple']
        linestyles = ['-', '--', '-.', ':', '-']
        
        for i, (strategy, (rounds, weights)) in enumerate(results.items()):
            plt.plot(rounds, weights, 
                    color=colors[i % len(colors)], 
                    linestyle=linestyles[i % len(linestyles)],
                    linewidth=2, 
                    label=f'{strategy}',
                    marker='o' if i < 2 else None,
                    markersize=4 if i < 2 else 0)
        
        plt.xlabel('训练进度 (轮次)', fontsize=12)
        plt.ylabel('动态权重 μ', fontsize=12)
        plt.title('APPAFL-SWIM动态权重演化对比', fontsize=14, fontweight='bold')
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.xlim(0, max(results['local_rounds'][0]))
        plt.ylim(0, 0.7)
        
        # 添加说明文本
        plt.text(0.02, 0.98, 
                '权重说明:\n• 高权重 = 更重视对比学习\n• 低权重 = 更重视分类任务\n• 适合异步联邦学习环境', 
                transform=plt.gca().transAxes, 
                fontsize=9,
                verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig('APPAFL-SWIM-Integration/dynamic_weight_evolution.png', dpi=300, bbox_inches='tight')
        print("✓ 权重演化图表已保存为 'dynamic_weight_evolution.png'")
        
    except ImportError:
        print("⚠ matplotlib未安装，跳过可视化")
    except Exception as e:
        print(f"⚠ 可视化生成失败: {e}")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)
    
    edge_cases = [
        {
            'name': '训练开始',
            'params': {'current_epoch': 0, 'total_epochs': 10, 'round_num': 0, 'total_rounds': 50, 'local_training_rounds': 0}
        },
        {
            'name': '训练结束',
            'params': {'current_epoch': 9, 'total_epochs': 10, 'round_num': 49, 'total_rounds': 50, 'local_training_rounds': 500}
        },
        {
            'name': '极端延迟',
            'params': {'current_epoch': 5, 'total_epochs': 10, 'round_num': 0, 'total_rounds': 50, 'local_training_rounds': 5}
        },
        {
            'name': '超长训练',
            'params': {'current_epoch': 15, 'total_epochs': 10, 'round_num': 60, 'total_rounds': 50, 'local_training_rounds': 1000}
        }
    ]
    
    for case in edge_cases:
        print(f"\n边界情况: {case['name']}")
        print("-" * 30)
        
        for strategy in ['local_rounds', 'global_progress', 'exponential_decay']:
            try:
                weight = calculate_async_dynamic_weight(strategy=strategy, **case['params'])
                print(f"  {strategy:15}: {weight:.4f}")
            except Exception as e:
                print(f"  {strategy:15}: 错误 - {e}")

def main():
    """主测试函数"""
    print("APPAFL-SWIM动态权重计算功能测试")
    print("=" * 60)
    
    # 运行所有测试
    try:
        # 测试1: 不同策略对比
        results = test_dynamic_weight_strategies()
        
        # 测试2: 异步场景
        test_async_scenarios()
        
        # 测试3: 边界情况
        test_edge_cases()
        
        # 测试4: 可视化
        visualize_weight_evolution(results)
        
        print("\n" + "=" * 60)
        print("✅ 所有动态权重测试完成！")
        print("=" * 60)
        
        # 总结
        print("\n📊 测试总结:")
        print("• local_rounds策略: 适合异步环境，基于客户端本地训练进度")
        print("• global_progress策略: 基于全局进度，可能受延迟影响")
        print("• fixed_schedule策略: 固定调度，不受全局状态影响")
        print("• exponential_decay策略: 指数衰减，对比学习权重快速降低")
        print("• 所有策略都确保权重在[0.1, 0.6]范围内")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
