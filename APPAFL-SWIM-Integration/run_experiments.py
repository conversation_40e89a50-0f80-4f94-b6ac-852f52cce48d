"""
APPAFL-SWIM整合项目实验运行脚本
用于快速运行和比较不同算法的性能
"""

import os
import sys
import subprocess
import time
import numpy as np
import argparse
from utils import *


def run_appafl_original(config):
    """运行APPAFL原始算法"""
    print("="*60)
    print("开始运行APPAFL原始算法...")
    print("="*60)
    
    # 修改main_appafl_original.py中的参数
    import main_appafl_original
    
    # 这里可以通过修改main文件中的args字典来调整参数
    # 或者通过命令行参数传递
    
    try:
        main_appafl_original.main()
        print("APPAFL原始算法运行完成！")
        return True
    except Exception as e:
        print(f"APPAFL原始算法运行失败: {e}")
        return False


def run_appafl_swim(config):
    """运行APPAFL-SWIM整合算法"""
    print("="*60)
    print("开始运行APPAFL-SWIM整合算法...")
    print("="*60)
    
    import main_appafl_swim
    
    try:
        main_appafl_swim.main()
        print("APPAFL-SWIM整合算法运行完成！")
        return True
    except Exception as e:
        print(f"APPAFL-SWIM整合算法运行失败: {e}")
        return False


def compare_results():
    """比较两种算法的结果"""
    print("="*60)
    print("比较算法性能...")
    print("="*60)
    
    try:
        # 加载结果
        appafl_path = './checkpoints_appafl_original/accuracy_record.npy'
        swim_path = './checkpoints_appafl_swim/accuracy_record.npy'
        
        if os.path.exists(appafl_path) and os.path.exists(swim_path):
            appafl_acc = np.load(appafl_path)
            swim_acc = np.load(swim_path)
            
            print(f"APPAFL原始算法最终准确率: {appafl_acc[-1]:.2f}%")
            print(f"APPAFL-SWIM算法最终准确率: {swim_acc[-1]:.2f}%")
            
            # 绘制比较图
            compare_algorithms(appafl_acc, swim_acc, 
                             save_path='./results/algorithm_comparison.png')
            
            # 绘制单独的训练曲线
            plot_training_curves(appafl_acc, 
                                save_path='./results/appafl_original_curve.png',
                                title='APPAFL Original Training Accuracy')
            
            plot_training_curves(swim_acc, 
                                save_path='./results/appafl_swim_curve.png',
                                title='APPAFL-SWIM Training Accuracy')
            
            # 保存比较结果
            results = {
                'appafl_accuracy': appafl_acc,
                'swim_accuracy': swim_acc,
                'appafl_final': appafl_acc[-1],
                'swim_final': swim_acc[-1],
                'improvement': swim_acc[-1] - appafl_acc[-1]
            }
            
            save_experiment_results(results, './results/comparison_results.npz')
            
            print("性能比较完成，结果已保存到 ./results/ 目录")
            
        else:
            print("未找到实验结果文件，请先运行实验")
            
    except Exception as e:
        print(f"结果比较失败: {e}")


def run_quick_test():
    """运行快速测试（减少轮数和客户端数量）"""
    print("="*60)
    print("运行快速测试...")
    print("="*60)
    
    # 这里可以实现一个快速测试版本
    # 修改参数以减少运行时间
    print("快速测试功能待实现...")


def main():
    parser = argparse.ArgumentParser(description='APPAFL-SWIM整合项目实验运行器')
    parser.add_argument('--mode', type=str, default='all', 
                       choices=['appafl', 'swim', 'all', 'compare', 'quick'],
                       help='运行模式')
    parser.add_argument('--num_clients', type=int, default=100,
                       help='客户端数量')
    parser.add_argument('--num_rounds', type=int, default=100,
                       help='通信轮数')
    parser.add_argument('--model_type', type=str, default='resnet18',
                       choices=['cnn', 'resnet18', 'resnet50'],
                       help='模型类型')
    
    args = parser.parse_args()
    
    # 创建结果目录
    mkdirs('./results')
    mkdirs('./checkpoints_appafl_original')
    mkdirs('./checkpoints_appafl_swim')
    
    # 设置随机种子
    set_random_seed(42)
    
    config = {
        'num_clients': args.num_clients,
        'num_rounds': args.num_rounds,
        'model_type': args.model_type
    }
    
    print("实验配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print()
    
    start_time = time.time()
    
    if args.mode == 'appafl':
        success = run_appafl_original(config)
        if success:
            print("APPAFL原始算法实验完成")
    
    elif args.mode == 'swim':
        success = run_appafl_swim(config)
        if success:
            print("APPAFL-SWIM整合算法实验完成")
    
    elif args.mode == 'all':
        print("运行完整实验对比...")
        
        # 运行APPAFL原始算法
        appafl_success = run_appafl_original(config)
        
        if appafl_success:
            print("\n等待5秒后运行SWIM算法...")
            time.sleep(5)
            
            # 运行APPAFL-SWIM算法
            swim_success = run_appafl_swim(config)
            
            if swim_success:
                print("\n开始比较结果...")
                compare_results()
            else:
                print("SWIM算法运行失败，无法进行比较")
        else:
            print("APPAFL算法运行失败，停止实验")
    
    elif args.mode == 'compare':
        compare_results()
    
    elif args.mode == 'quick':
        run_quick_test()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n总运行时间: {total_time/60:.2f} 分钟")
    print("实验完成！")


def print_system_info():
    """打印系统信息"""
    import torch
    
    print("="*60)
    print("系统信息:")
    print(f"  Python版本: {sys.version}")
    print(f"  PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  CUDA版本: {torch.version.cuda}")
        print(f"  GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    print("="*60)


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'torch', 'torchvision', 'numpy', 'matplotlib', 
        'seaborn', 'scikit-learn', 'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    else:
        print("所有依赖包已安装")
        return True


if __name__ == "__main__":
    print("APPAFL-SWIM整合项目实验运行器")
    print_system_info()
    
    if check_dependencies():
        main()
    else:
        print("请先安装缺少的依赖包")
        sys.exit(1)
