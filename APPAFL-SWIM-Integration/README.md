# APPAFL-SWIM 整合项目

本项目整合了APPAFL和SWIM两种联邦学习算法，提供了两种不同的客户端训练方式，同时使用SWIM的ResNet模型作为基础训练模型。

## 项目结构

```
APPAFL-SWIM-Integration/
├── models.py                    # 整合的模型定义
├── data_utils.py               # 数据处理工具（使用APPAFL的数据划分方式）
├── main_appafl_original.py     # 使用原APPAFL客户端训练算法
├── main_appafl_swim.py         # 使用SWIM客户端训练算法
├── utils.py                    # 通用工具函数
└── README.md                   # 项目说明文档
```

## 主要特性

### 1. 客户端训练算法
- **原APPAFL算法**: 支持异步联邦学习，包含延迟处理机制
- **SWIM算法**: 基于对比学习的联邦学习，使用滑动窗口机制

### 2. 模型支持
- **ResNet18**: 适配CIFAR数据集的ResNet-18模型
- **ResNet50**: 适配CIFAR数据集的ResNet-50模型
- **投影版**: 带投影头的SWIM模型，支持对比学习
- **非投影版**: 不带投影头的简化SWIM模型

### 3. 数据集划分
- 使用APPAFL的数据集划分方式
- 支持IID和Non-IID数据分布
- 基于CIFAR-10数据集

## 安装依赖

```bash
pip install torch torchvision numpy matplotlib seaborn scikit-learn scipy
```

## 使用方法

### 1. 运行原APPAFL算法

```bash
python main_appafl_original.py
```

**主要参数**:
- `num_of_clients`: 客户端总数 (默认: 100)
- `cfraction`: 每轮参与的客户端比例 (默认: 0.1)
- `num_comm`: 通信轮数 (默认: 100)
- `model_type`: 模型类型 ('cnn', 'resnet18', 'resnet50')
- `IID`: 数据分布 (True为IID, False为Non-IID)

### 2. 运行APPAFL-SWIM整合算法

```bash
python main_appafl_swim.py
```

**主要参数**:
- `num_of_clients`: 客户端总数 (默认: 100)
- `cfraction`: 每轮参与的客户端比例 (默认: 0.1)
- `num_comm`: 通信轮数 (默认: 100)
- `model_type`: 模型类型 ('resnet18', 'resnet50')
- `use_project_head`: 是否使用投影头 (默认: True)
- `mu`: SWIM算法的mu参数 (默认: 0.1)
- `temperature`: 对比学习温度参数 (默认: 0.5)

## 模型配置

### 1. 创建模型

```python
from models import create_model

# 创建APPAFL模型
appafl_model = create_model("appafl", base_model="cnn")

# 创建SWIM模型（带投影头）
swim_model = create_model("swim", base_model="resnet18", use_project_head=True)

# 创建SWIM模型（不带投影头）
swim_model_no_head = create_model("swim", base_model="resnet18", use_project_head=False)
```

### 2. 数据加载

```python
from data_utils import create_data_loaders

# 创建客户端数据
clients = create_data_loaders(
    dataset_name='cifar10',
    is_iid=False,           # Non-IID数据分布
    num_clients=100,
    device='cuda'
)
```

## 算法特点

### APPAFL原始算法
- **异步联邦学习**: 支持客户端异步参与训练
- **延迟处理**: 对延迟客户端使用不同的学习率和聚合策略
- **陈旧模型管理**: 维护历史全局模型用于延迟客户端训练

### SWIM算法
- **对比学习**: 使用对比学习提升特征表示质量
- **滑动窗口机制**: 收集历史模型表示进行对比学习
- **动态系数**: 在训练过程中动态平衡分类学习和特征学习

### 整合优势
- **保留APPAFL的异步机制**: 支持客户端延迟和异步聚合
- **引入SWIM的对比学习**: 提升模型的特征学习能力
- **灵活的模型选择**: 支持多种模型架构和配置
- **统一的数据划分**: 使用APPAFL的数据划分方式确保公平比较

## 实验结果

训练完成后，结果将保存在以下位置：
- `./checkpoints_appafl_original/`: APPAFL原始算法结果
- `./checkpoints_appafl_swim/`: APPAFL-SWIM整合算法结果

每个目录包含：
- `final_global_model.pth`: 最终全局模型
- `accuracy_record.npy`: 准确率记录
- `global_model_round_*.pth`: 各轮次的模型检查点

## 性能分析

使用工具函数进行性能分析：

```python
from utils import *

# 加载准确率记录
appafl_acc = np.load('./checkpoints_appafl_original/accuracy_record.npy')
swim_acc = np.load('./checkpoints_appafl_swim/accuracy_record.npy')

# 比较算法性能
compare_algorithms(appafl_acc, swim_acc, save_path='./algorithm_comparison.png')

# 绘制训练曲线
plot_training_curves(appafl_acc, save_path='./appafl_training_curve.png', 
                    title='APPAFL Original Training Accuracy')
plot_training_curves(swim_acc, save_path='./swim_training_curve.png', 
                    title='APPAFL-SWIM Training Accuracy')
```

## 参数调优建议

### 1. 学习率设置
- 正常客户端: 0.01-0.05
- 延迟客户端: 0.02-0.1 (APPAFL)
- SWIM算法: 0.01-0.03

### 2. SWIM特定参数
- `mu`: 0.05-0.2 (对比学习权重)
- `temperature`: 0.3-0.7 (对比学习温度)
- `out_dim`: 128-512 (投影层维度)

### 3. 异步参数
- `percentageOfStale`: 0.2-0.5 (延迟客户端比例)
- `threshold`: 2-5 (陈旧模型聚合阈值)
- `stale_threshold`: 4-8 (最大延迟轮数)

## 注意事项

1. **内存使用**: SWIM算法需要维护历史模型，会占用更多内存
2. **计算复杂度**: 对比学习会增加计算开销
3. **收敛速度**: SWIM算法可能需要更多轮次才能收敛
4. **参数敏感性**: 对比学习参数对性能影响较大，需要仔细调优

## 扩展功能

项目支持以下扩展：
- 添加新的基础模型架构
- 实现其他对比学习损失函数
- 支持更多数据集
- 添加差分隐私保护
- 实现模型压缩和量化
