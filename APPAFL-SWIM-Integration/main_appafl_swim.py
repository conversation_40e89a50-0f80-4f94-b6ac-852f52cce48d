# APPAFL-SWIM融合版本
# 将SWIM算法完整融合到APPAFL的异步联邦学习框架中
import os
import argparse
import copy
import logging
from Crypto.Util import number
from tqdm import tqdm
import random
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
from torch.utils.data import DataLoader

# 导入模型和数据处理模块
from models import Mnist_2NN, Mnist_CNN, Cifar_CNN, CNNCifar, ModelFedCon, ModelFedCon_noheader, create_model
from data_utils import ClientsGroup, Client, GetDataSet

# 创建命令行参数解析器，用于APPAFL-SWIM融合算法
parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter, description="APPAFL-SWIM融合联邦学习算法")

# ==================== 基础系统参数 ====================
parser.add_argument('-g', '--gpu', type=str, default='0',help='指定使用的GPU设备ID (例如: 0,1,2,3)')
parser.add_argument('-nc', '--num_of_clients', type=int, default=20,help='联邦学习中的客户端总数量')
parser.add_argument('-cf', '--cfraction', type=float, default=1,help='客户端参与比例 (0表示1个客户端，1表示全部客户端参与)')

# ==================== 训练相关参数 ====================
parser.add_argument('-E', '--epoch', type=int, default=10,help='每个客户端的本地训练轮数')
parser.add_argument('-B', '--batchsize', type=int, default=50,help='本地训练的批次大小')
parser.add_argument('-mn', '--model_name', type=str, default='CNNCifar',help='要训练的模型名称 (CNNCifar, mnist_2nn, mnist_cnn等)')
parser.add_argument('-lr', "--learning_rate", type=float, default=0.1,help="正常客户端的学习率 (使用原论文的默认值)")
parser.add_argument('-dlr', "--delayed_learning_rate", type=float, default=0.1,help="延迟客户端的学习率 (用于补偿陈旧性)")

# ==================== 联邦学习系统参数 ====================
parser.add_argument('-vf', "--val_freq", type=int, default=1,help="模型验证频率 (每多少轮通信进行一次验证)")
parser.add_argument('-sf', '--save_freq', type=int, default=1,help='全局模型保存频率 (每多少轮通信保存一次)')
parser.add_argument('-ncomm', '--num_comm', type=int, default=100,help='联邦学习的总通信轮数')
parser.add_argument('-sp', '--save_path', type=str, default='./checkpoints',help='模型检查点的保存路径')
parser.add_argument('-iid', '--IID', type=int, default=0,help='数据分配方式 (0=Non-IID, 1=IID)')
parser.add_argument('--mu', type=float, default=0.01,help='近端项常数 (用于FedProx和SWIM算法)')
parser.add_argument('--q_width', type=int, default=16,help='量化位宽 (用于模型压缩)')

# ==================== SWIM算法专用参数 ====================
parser.add_argument('--alg', type=str, default='appafl',help='算法类型选择: appafl(异步联邦学习), swim(对比学习), appafl_swim(融合算法)')
parser.add_argument('--temperature', type=float, default=0.5,help='对比学习的温度参数 (控制相似度计算的锐度，越小越锐利)')
parser.add_argument('--model_type', type=str, default='appafl',help='模型架构类型: appafl(传统CNN), swim(带投影头的ResNet)')
parser.add_argument('--base_model', type=str, default='cnn',help='基础模型选择: cnn(传统CNN), resnet18(ResNet-18), resnet50(ResNet-50)')
parser.add_argument('--use_project_head', type=int, default=1,help='是否使用投影头 (1=使用投影头进行对比学习, 0=不使用投影头)')
parser.add_argument('--out_dim', type=int, default=256,help='投影头的输出维度 (对比学习特征空间的维度)')
parser.add_argument('--pool_size', type=int, default=5,help='历史模型池的大小 (SWIM算法中保存的历史模型数量)')


def tes_mkdir(path):
    if not os.path.isdir(path):
        os.mkdir(path)


k0 = 2048
k1 = 20
k2 = 160
p = number.getPrime(k0)
q = number.getPrime(k0)
N = p * q
# sk=(p,L)
# pk=(k0,k1,k2,N)
L = number.getPrime(k2)


def she_enc(p, L, m):  # m明文值
    r = random.getrandbits(k2)
    r1 = random.getrandbits(k0)
    return ((r * L + m) * (1 + r1 * p)) % N


def she_dec(p, L, c):  # c密文值值
    m = (c % p) % L
    if m < L / 2:
        return m
    else:
        return m - L


# def StaleClientDecide(num_in_comm, PercentageOfStale):
#     # 这个函数用来在num_in_comm个客户端中选择百分之PercentageOfStale个客户端作为掉线的客户端
#     clients_in_comm = ['client{}'.format(i) for i in range(num_in_comm)]  # 生成num_in_comm个客户端
#     index = [i for i in order[0:num_in_comm][num_in_comm*PercentageOfStale]]  # 随机生成指定数目延迟客户端的索引
#     return clients_in_comm, index

def flagUpdate(flag, time_stamp, epoch, percentageOfStale):
    num = flag.count(0)  # 统计当前轮次中有几个延迟客户端
    if num == 0:
        index = np.random.permutation(num_in_comm)[0:int(num_in_comm * percentageOfStale - num)]  # 随机生成指定数目延迟客户端的索引
        # print(index)
        for j in range(len(index)):  # 对于上一轮没有延迟的客户，将延迟标志设为0，时间戳设为上一轮次，上一轮延迟则不做更改
            if flag[index[j]] != 0:
                flag[index[j]] = 0
                time_stamp[index[j]] = epoch
    return flag, time_stamp


def stale_timeUpdate(flag, stale_time, stale_threshold):
    index = [i for i, x in enumerate(flag) if x == 0]  # 延迟客户端的索引
    for j in range(len(index)):
        # print(j)
        if stale_time[index[j]] == 0:  # 如果该客户端上一轮没有延迟则设置延迟轮数，如果上一轮延迟则不做更改
            # print(index[j])
            stale_time[index[j]] = np.random.permutation(stale_threshold)[0] + 1  # 随机确定客户端延迟的轮数
            # print(stale_time[index[j]])
    return stale_time


# def quantize_per_layer(party, r_maxs, bit_width=16):
#     result = []
#     for component, r_max in zip(party, r_maxs):
#         x, _ = encryption.quantize_matrix_stochastic(component, bit_width=bit_width, r_max=r_max)
#         result.append(x)
#     return np.array(result)
#
#
# def unquantize_per_layer(party, r_maxs, bit_width=16):
#     result = []
#     for component, r_max in zip(party, r_maxs):
#         result.append(encryption.unquantize_matrix(component, bit_width=bit_width, r_max=r_max).astype(np.float32))
#     return np.array(result)


def train_net_swim(net_id, net, global_net, previous_nets, train_dataloader, test_dataloader, epochs, lr,
                     args_optimizer, mu, temperature, args, round, device="cpu"):
    """
    SWIM算法的核心训练函数
    实现了基于对比学习的联邦学习训练过程

    Args:
        net_id: 网络ID
        net: 当前客户端的网络
        global_net: 全局网络模型
        previous_nets: 历史网络模型列表
        train_dataloader: 训练数据加载器
        test_dataloader: 测试数据加载器
        epochs: 训练轮数
        lr: 学习率
        args_optimizer: 优化器类型
        mu: SWIM算法的mu参数
        temperature: 对比学习的温度参数
        args: 其他参数
        round: 当前通信轮次
        device: 运行设备

    Returns:
        trainacc: 训练准确率
        testacc: 测试准确率
    """
    print(f'Training network {net_id} with SWIM algorithm')

    # 将网络移动到GPU
    net.cuda()
    if global_net:
        global_net.cuda()

    # 设置优化器
    if args_optimizer == 'adam':
        optimizer = torch.optim.Adam(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, weight_decay=1e-4)
    elif args_optimizer == 'amsgrad':
        optimizer = torch.optim.Adam(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, weight_decay=1e-4,
                                   amsgrad=True)
    elif args_optimizer == 'sgd':
        optimizer = torch.optim.SGD(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, momentum=0.9,
                                  weight_decay=1e-4)

    # 损失函数
    criterion = nn.CrossEntropyLoss().cuda()
    cos = torch.nn.CosineSimilarity(dim=-1).cuda()

    # 初始化历史特征缓存
    C = 10  # 缓存大小
    Z_prev = [None] * C
    cnt = 0

    # 训练循环
    epoch_loss_collector = []
    epoch_loss1_collector = []  # 分类损失
    epoch_loss2_collector = []  # 对比损失

    for epoch in range(epochs):
        for batch_idx, (x, target) in enumerate(train_dataloader):
            # 将数据移动到GPU
            x, target = x.cuda(), target.cuda()

            optimizer.zero_grad()
            x.requires_grad = False
            target.requires_grad = False
            target = target.long()

            # 前向传播：获取当前网络的特征和输出
            if hasattr(net, 'l3'):  # SWIM模型
                _, pro1, out = net(x)          # pro1: 当前网络的投影特征
                if global_net:
                    _, pro2, _ = global_net(x)     # pro2: 全局网络的投影特征
                else:
                    pro2 = pro1  # 如果没有全局模型，使用自身特征
            else:  # 普通模型，没有投影头
                out = net(x)
                pro1 = out  # 使用输出作为特征
                pro2 = out

            # 计算分类损失
            loss1 = criterion(out, target)

            # 计算对比学习损失
            loss2 = torch.tensor(0.0).cuda()
            if hasattr(net, 'l3') and len(previous_nets) > 0 and round > 0:
                # 计算正样本相似度（当前网络与全局网络的相似度）
                posi = torch.exp(torch.mean(cos(pro1, pro2).reshape(-1, 1)) / temperature)

                # 初始化负样本相似度
                nega = torch.tensor(0.0).cuda()

                # 遍历历史网络，计算对比学习损失
                for previous_net in previous_nets:
                    if previous_net is not None:
                        previous_net.cuda()
                        try:
                            _, pro3, _ = previous_net(x)  # pro3: 历史网络的投影特征

                            # 将历史特征存储到缓存中（循环缓存）
                            Z_prev[cnt % C] = pro3.detach()

                            # 遍历缓存中的历史特征
                            for i in range(C):
                                if Z_prev[i] is not None:
                                    # 计算当前特征与历史特征的相似度
                                    t = torch.mean(cos(pro1, Z_prev[i]).reshape(-1, 1))
                                    if t >= 0.5:
                                        # 如果相似度高，作为正样本
                                        posi = posi + torch.exp(t / temperature)
                                    else:
                                        # 如果相似度低，作为负样本
                                        nega = nega + torch.exp(t / temperature)
                        except Exception as e:
                            print(f"Error processing previous net: {e}")
                        finally:
                            # 将历史网络移回CPU以节省GPU内存
                            previous_net.to('cpu')

                # 计算对比学习损失
                if posi > 0 and (posi + nega) > 0:
                    loss2 = torch.mean(-torch.log(posi / (posi + nega)))

            # 动态调整损失权重（随着训练进行，对比损失权重逐渐减小）
            if hasattr(args, 'num_comm'):
                mu_dynamic = 0.5 - (round + 1) / (2 * args['num_comm'])
            else:
                mu_dynamic = mu
            mu_dynamic = max(0.1, mu_dynamic)  # 确保权重不会太小

            # 总损失
            loss = (1 - mu_dynamic) * loss1 + mu_dynamic * loss2

            # 反向传播和参数更新
            loss.backward()
            optimizer.step()

            # 记录损失
            cnt += 1
            epoch_loss_collector.append(loss.item())
            epoch_loss1_collector.append(loss1.item())
            epoch_loss2_collector.append(loss2.item())

    # 计算训练和测试准确率
    train_acc = compute_accuracy(net, train_dataloader, device=device)
    test_acc = compute_accuracy(net, test_dataloader, device=device) if test_dataloader else 0.0

    print(f'>> Training accuracy: {train_acc:.4f}')
    print(f'>> Test accuracy: {test_acc:.4f}')
    print(f'>> Average loss: {np.mean(epoch_loss_collector):.4f}')
    print(f'>> Average classification loss: {np.mean(epoch_loss1_collector):.4f}')
    print(f'>> Average contrastive loss: {np.mean(epoch_loss2_collector):.4f}')

    # 将网络移回CPU
    net.to('cpu')

    return train_acc, test_acc


def compute_accuracy(net, dataloader, device="cpu"):
    """计算模型准确率"""
    was_training = False
    if net.training:
        net.eval()
        was_training = True

    true_labels_list, pred_labels_list = np.array([]), np.array([])

    if type(dataloader) == type([1]):
        pass
    else:
        with torch.no_grad():
            for batch_idx, (x, target) in enumerate(dataloader):
                x, target = x.to(device), target.to(device)
                if hasattr(net, 'l3'):  # SWIM模型
                    _, _, out = net(x)
                else:  # 普通模型
                    out = net(x)
                _, pred_label = torch.max(out.data, 1)

                true_labels_list = np.append(true_labels_list, target.cpu().numpy())
                pred_labels_list = np.append(pred_labels_list, pred_label.cpu().numpy())

    if was_training:
        net.train()

    return np.sum(true_labels_list == pred_labels_list) / len(true_labels_list)


if __name__ == "__main__":
    args = parser.parse_args()
    args = args.__dict__

    tes_mkdir(args['save_path'])

    os.environ['CUDA_VISIBLE_DEVICES'] = args['gpu']
    dev = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

    # 根据算法类型创建模型
    net = None
    if args['alg'] == 'swim' or args['model_type'] == 'swim':
        # 使用SWIM模型
        use_project_head = bool(args['use_project_head'])
        net = create_model(
            model_type="swim",
            base_model=args['base_model'],
            use_project_head=use_project_head,
            out_dim=args['out_dim'],
            n_classes=10
        )
        print(f"创建SWIM模型: {args['base_model']}, 投影头: {use_project_head}")
    else:
        # 使用APPAFL原始模型
        if args['model_name'] == 'mnist_2nn':
            net = Mnist_2NN()
        elif args['model_name'] == 'mnist_cnn':
            net = Mnist_CNN()
        elif args['model_name'] == 'cifar10_cnn':
            net = Cifar_CNN()
        elif args['model_name'] == 'CNNCifar':
            net = CNNCifar()
        print(f"创建APPAFL模型: {args['model_name']}")

    if torch.cuda.device_count() > 1:
        print("Let's use", torch.cuda.device_count(), "GPUs!")
        net = torch.nn.DataParallel(net)
    net = net.to(dev)

    loss_func = F.cross_entropy
    # opti = optim.SGD(net.parameters(), lr=args['learning_rate'])

    myClients = ClientsGroup('cifar10', args['IID'], args['num_of_clients'], dev, args["mu"])
    testDataLoader = myClients.test_data_loader

    num_in_comm = int(max(args['num_of_clients'] * args['cfraction'], 1))

    AsyAccuracy = []  # 存放模型准确率

    global_parameters = {}  # 最新的全局模型
    stale_global_parameters = {}  # 陈旧的全局模型
    for key, var in net.state_dict().items():
        global_parameters[key] = var.clone()
        stale_global_parameters[key] = var.clone()

    # 异步联邦学习相关参数设置
    client_nums = args['num_of_clients']
    percentageOfStale = 0.4     # 延迟客户端所占总客户端数目的百分比
    stale_threshold = 4         # 每个客户端延迟的阈值（最大延迟轮数）
    flag = [1] * client_nums            # 客户端参与训练标志，0代表此轮不参与训练，1代表此轮参与训练
    weight = [1] * client_nums          # 客户端在聚合时的权重
    stale_time = [0] * client_nums      # 客户端延时的轮次，0表示不延迟
    time_stamp = [0] * client_nums      # 时间戳，表示客户端参与的最近一次训练的全局轮次
    threshold = 6              # 陈旧模型聚合阈值，超过此阈值的延迟模型不参与聚合
    shape = []                 # 存储客户端每层模型参数的维度（用于加密后的重塑）

    # 打印关键参数信息
    print("="*60)
    print("异步联邦学习参数设置:")
    print(f"  算法类型: {args['alg']}")
    print(f"  模型类型: {args['model_type']}")
    print(f"  总客户端数: {client_nums}")
    print(f"  每轮参与客户端数: {num_in_comm}")
    print(f"  延迟客户端比例: {percentageOfStale}")
    print(f"  正常客户端学习率: {args['learning_rate']}")
    print(f"  延迟客户端学习率: {args['delayed_learning_rate']}")
    print(f"  陈旧模型聚合阈值: {threshold}")
    print(f"  最大延迟轮数: {stale_threshold}")
    if args['alg'] == 'swim':
        print(f"  SWIM温度参数: {args['temperature']}")
        print(f"  SWIM mu参数: {args['mu']}")
        print(f"  历史模型池大小: {args['pool_size']}")
    print("="*60)

    # SWIM算法相关初始化
    old_nets_pool = []  # 历史模型池
    nets_pool = {}  # 客户端网络池

    if args['alg'] == 'swim':
        # 为每个客户端创建网络副本用于SWIM训练
        for client_id in range(client_nums):
            if args['model_type'] == 'swim':
                use_project_head = bool(args['use_project_head'])
                client_net = create_model(
                    model_type="swim",
                    base_model=args['base_model'],
                    use_project_head=use_project_head,
                    out_dim=args['out_dim'],
                    n_classes=10
                )
            else:
                # 使用与全局模型相同的结构
                if args['model_name'] == 'CNNCifar':
                    client_net = CNNCifar()
                else:
                    client_net = CNNCifar()  # 默认使用CNNCifar

            client_net.load_state_dict(net.state_dict())
            nets_pool[client_id] = client_net.to('cpu')

        print(f"初始化了 {len(nets_pool)} 个客户端网络用于SWIM训练")
    else:
        # 对于非SWIM算法，也初始化网络池以保持代码一致性
        for client_id in range(client_nums):
            if args['model_name'] == 'CNNCifar':
                client_net = CNNCifar()
            else:
                client_net = CNNCifar()  # 默认使用CNNCifar
            client_net.load_state_dict(net.state_dict())
            nets_pool[client_id] = client_net.to('cpu')

    for i in range(args['num_comm']):
        print("communicate round {}".format(i + 1))
        number = 0
        order = np.random.permutation(args['num_of_clients'])
        clients_in_comm = ['client{}'.format(i) for i in order[0:num_in_comm]]

        sum_parameters = None

        u = []  # 没有参与训练的客户端。

        if i != 0:
            flag, time_stamp = flagUpdate(flag, time_stamp, i, percentageOfStale)
            stale_time = stale_timeUpdate(flag, stale_time, stale_threshold)
            print("客户端的延迟轮次为：{}".format(stale_time))

            # 对于正常客户端：
            id = [key for key, value in enumerate(flag) if value == 1]  # 获取这些客户端的索引
            for m in range(len(id)):
                weight[id[m]] = 1  # 设置权重
                # print('客户{}的权重分数为{}'.format(id[m], weight[id[m]]))
            # 对于延迟客户端：
            iid = [key for key, value in enumerate(flag) if value == 0]  # 获取这些客户端的索引
            s = []  # 存放参与此轮训练的客户端中延迟客户端的索引
            for m in range(len(iid)):

                stale_time[iid[m]] -= 1
                # print(stale_time[iid[m]])
                if stale_time[iid[m]] == 0:
                    s.append(iid[m])
                    flag[iid[m]] = 1
                    # weight[iid[m]] = (i + 1) / (((i+1) - time_stamp[iid[m]])*args['num_comm'])  # 设置权重
                    # weight[iid[m]] = ((i + 1)/args['num_comm']) * (1 - ((i+1) - time_stamp[iid[m]])/(i + 1)) # 设置权重
                    if (i + 1) - time_stamp[iid[m]] > threshold:
                        weight[iid[m]] = 0
                        number += 1
                    else:
                        weight[iid[m]] = 2**(-((i+1)-time_stamp[iid[m]]))  # 设置权重
                    # weight[iid[m]] = 2**(-((i+1)-time_stamp[iid[m]]))  # 设置权重
                    print('客户{}的权重分数为{}'.format(iid[m], weight[iid[m]]))
            print("参与此轮训练的延迟客户端：{}".format(s))
            print("未参与此轮训练的延迟客户端：{}".format(list(set(iid) - set(s))))
            # 对于未参与训练的客户端：
            if i < 100:
                uu = list(set(iid) - set(s))  # 存放未参与此轮训练的客户端
                for m in range(len(uu)):
                    # flag[u[m]] = 1
                    if (i + 1) - time_stamp[uu[m]] <= threshold:
                        # weight[u[m]] = ((i + 1)/args['num_comm']) * (1 - ((i+1) - time_stamp[iid[m]])/(i + 1)) # 设置权重
                        # weight[uu[m]] = 1  # 设置权重
                        weight[uu[m]] = 2 ** (-((i + 1) - time_stamp[uu[m]]))  # 设置权重
                        u.append(uu[m])
                        print("{}的权重分数为{}(此轮未到达但是用该客户端的陈旧模型)".format(uu[m], weight[uu[m]]))
                    else:
                        print("此轮未使用客户端{}的陈旧模型参与聚合".format(uu[m]))
            else:
                u = []
        # 获取参与此轮训练的客户端的索引（包括正常的和延迟的：）并参与训练
        iiiid = [key for key, value in enumerate(flag) if value == 1]
        iiid = iiiid + u
        print("参与此轮训练的所有客户端：{}".format(iiiid))
        time = []
        for b in range(client_nums):
            time.append(time_stamp[b])
        print("客户端的时间戳：{}".format(time))
        # 计算所有客户端权重分数的和
        sum_weight = 0
        for m in range(len(iiid)):
            sum_weight = sum_weight + weight[iiid[m]]

        print(f"总权重: {sum_weight}, 参与聚合的客户端数: {len(iiid)}")
        print(f"权重分布: {[weight[iiid[m]] for m in range(len(iiid))]}")

        rmax = []
        k = []

        for m in range(len(iiid)):
            client_id = iiid[m]
            print('******{}******'.format(client_id))

            # 调试信息：显示客户端状态
            staleness = i - time_stamp[client_id]
            print(f"客户端 {client_id}: 当前轮次={i}, 时间戳={time_stamp[client_id]}, 陈旧程度={staleness}")

            local_gradients = []
            # 对于上一轮参与了训练这一轮也参与训练的客户端，说明客户端的模型不是延迟模型
            if staleness <= 1 or i == 0:  # 修复：使用更清晰的条件
                print(f"客户端 {client_id} 使用当前全局模型进行训练")

                if args['alg'] == 'swim':
                    # 使用SWIM算法进行训练
                    print(f"使用SWIM算法训练客户端 {client_id}")

                    # 准备当前客户端的网络
                    current_net = nets_pool[client_id].to(dev)
                    current_net.load_state_dict(global_parameters, strict=True)

                    # 准备全局模型副本
                    global_net_copy = None
                    if args['model_type'] == 'swim':
                        use_project_head = bool(args['use_project_head'])
                        global_net_copy = create_model(
                            model_type="swim",
                            base_model=args['base_model'],
                            use_project_head=use_project_head,
                            out_dim=args['out_dim'],
                            n_classes=10
                        ).to(dev)
                        global_net_copy.load_state_dict(global_parameters, strict=True)

                    # 准备历史模型
                    prev_models = []
                    for pool_round in old_nets_pool:
                        if client_id in pool_round:
                            prev_models.append(pool_round[client_id])

                    # 创建客户端数据加载器
                    client_train_data = myClients.clients_set[f'client{client_id}'].train_ds
                    client_train_loader = DataLoader(client_train_data, batch_size=args['batchsize'], shuffle=True)

                    # 执行SWIM训练
                    train_acc, test_acc = train_net_swim(
                        net_id=client_id,
                        net=current_net,
                        global_net=global_net_copy,
                        previous_nets=prev_models,
                        train_dataloader=client_train_loader,
                        test_dataloader=testDataLoader,
                        epochs=args['epoch'],
                        lr=args['learning_rate'],
                        args_optimizer='sgd',
                        mu=args['mu'],
                        temperature=args['temperature'],
                        args=args,
                        round=i,
                        device=dev
                    )

                    # 获取训练后的参数
                    local_parameters = current_net.state_dict()

                    # 将网络移回CPU并更新池中的网络
                    current_net.to('cpu')
                    nets_pool[client_id].load_state_dict(local_parameters)

                    if global_net_copy:
                        global_net_copy.to('cpu')
                        del global_net_copy
                else:
                    # 使用APPAFL原始训练方法
                    opti = optim.SGD(net.parameters(), lr=args['learning_rate'])
                    local_parameters = myClients.clients_set['client{}'.format(client_id)].localUpdate(args['epoch'],
                                                                                                   args['batchsize'], net,
                                                                                                   loss_func, opti,
                                                                                                   global_parameters,
                                                                                                   args["mu"])


            # 对于上一轮没参加训练这一轮参加训练的客户端，说明该客户端上传的模型是延迟的模型
            else:
                print(f"客户端 {client_id} 是延迟客户端，陈旧程度: {staleness}")
                # 获取客户端最后一次参与训练所对应的全局模型，并用这个全局模型获得客户端的延迟模型
                # 延迟客户端使用更高的学习率来补偿陈旧性，但要确保不会太高
                delayed_lr = args['delayed_learning_rate']
                # 安全检查：确保延迟客户端学习率不会过高
                if delayed_lr > 0.1:
                    print(f"警告：延迟客户端学习率 {delayed_lr} 过高，调整为 0.02")
                    delayed_lr = 0.02
                print(f"延迟客户端 {client_id} 使用学习率: {delayed_lr} (正常客户端: {args['learning_rate']})")

                # 安全加载陈旧模型
                stale_round = time_stamp[client_id] - 1
                stale_model_path = os.path.join(args['save_path'], '{}_num_comm{}'.format(args['model_name'], stale_round))
                try:
                    if os.path.exists(stale_model_path) and stale_round >= 0:
                        stale_global_parameters = torch.load(stale_model_path)
                        print(f"加载延迟客户端 {client_id} 的陈旧模型：轮次 {stale_round}")
                    else:
                        print(f"警告：陈旧模型文件不存在 (轮次 {stale_round})，使用当前全局模型替代")
                        stale_global_parameters = global_parameters
                except Exception as e:
                    print(f"警告：加载陈旧模型失败 {e}，使用当前全局模型替代")
                    stale_global_parameters = global_parameters

                if args['alg'] == 'swim':
                    # 使用SWIM算法训练延迟客户端
                    print(f"使用SWIM算法训练延迟客户端 {client_id}")

                    # 准备当前客户端的网络
                    current_net = nets_pool[client_id].to(dev)
                    current_net.load_state_dict(stale_global_parameters, strict=True)

                    # 准备陈旧的全局模型副本
                    stale_global_net = None
                    if args['model_type'] == 'swim':
                        use_project_head = bool(args['use_project_head'])
                        stale_global_net = create_model(
                            model_type="swim",
                            base_model=args['base_model'],
                            use_project_head=use_project_head,
                            out_dim=args['out_dim'],
                            n_classes=10
                        ).to(dev)
                        stale_global_net.load_state_dict(stale_global_parameters, strict=True)

                    # 准备历史模型
                    prev_models = []
                    for pool_round in old_nets_pool:
                        if client_id in pool_round:
                            prev_models.append(pool_round[client_id])

                    # 创建客户端数据加载器
                    client_train_data = myClients.clients_set[f'client{client_id}'].train_ds
                    client_train_loader = DataLoader(client_train_data, batch_size=args['batchsize'], shuffle=True)

                    # 执行SWIM训练（使用延迟学习率）
                    train_acc, test_acc = train_net_swim(
                        net_id=client_id,
                        net=current_net,
                        global_net=stale_global_net,
                        previous_nets=prev_models,
                        train_dataloader=client_train_loader,
                        test_dataloader=testDataLoader,
                        epochs=args['epoch'],
                        lr=delayed_lr,
                        args_optimizer='sgd',
                        mu=args['mu'],
                        temperature=args['temperature'],
                        args=args,
                        round=i,
                        device=dev
                    )

                    # 获取训练后的参数
                    local_parameters = current_net.state_dict()

                    # 将网络移回CPU并更新池中的网络
                    current_net.to('cpu')
                    nets_pool[client_id].load_state_dict(local_parameters)

                    if stale_global_net:
                        stale_global_net.to('cpu')
                        del stale_global_net
                else:
                    # 使用APPAFL原始训练方法
                    opti = optim.SGD(net.parameters(), lr=delayed_lr)
                    local_parameters = myClients.clients_set['client{}'.format(client_id)].localUpdate(args['epoch'],
                                                                                                   args['batchsize'], net,
                                                                                                   loss_func, opti,
                                                                                                   stale_global_parameters,
                                                                                                   args["mu"])

            # 加权聚合
            print("Aggregate....")
            weight_ratio = weight[client_id] / sum_weight
            print(f"客户端 {client_id} 聚合权重: {weight_ratio:.4f}")

            if sum_parameters is None:
                sum_parameters = {}
                for key, var in local_parameters.items():
                    sum_parameters[key] = var * weight_ratio
            else:
                for key, var in local_parameters.items():
                    sum_parameters[key] = sum_parameters[key] + var * weight_ratio

        for m in range(len(iiiid)):
            time_stamp[iiiid[m]] = i + 1  # 设置本轮到达的客户端的时间戳为当前训练轮次

        # 更新全局参数
        for var in global_parameters:
            global_parameters[var] = sum_parameters[var]

        # SWIM算法：更新历史模型池
        if args['alg'] == 'swim':
            # 保存当前轮次的所有客户端模型到历史池
            current_round_nets = {}
            for client_id in range(client_nums):
                # 创建当前客户端模型的深拷贝
                if args['model_type'] == 'swim':
                    use_project_head = bool(args['use_project_head'])
                    client_net_copy = create_model(
                        model_type="swim",
                        base_model=args['base_model'],
                        use_project_head=use_project_head,
                        out_dim=args['out_dim'],
                        n_classes=10
                    )
                else:
                    client_net_copy = CNNCifar()

                # 加载当前客户端的参数
                if client_id in nets_pool:
                    client_net_copy.load_state_dict(nets_pool[client_id].state_dict())
                else:
                    client_net_copy.load_state_dict(global_parameters)

                current_round_nets[client_id] = client_net_copy.to('cpu')

            # 将当前轮次的模型添加到历史池
            old_nets_pool.append(current_round_nets)

            # 限制历史池大小
            if len(old_nets_pool) > args['pool_size']:
                # 删除最旧的模型
                old_round = old_nets_pool.pop(0)
                # 清理内存
                for net in old_round.values():
                    del net

            print(f"历史模型池大小: {len(old_nets_pool)}")

        with torch.no_grad():
            if (i + 1) % args['val_freq'] == 0:
                net.load_state_dict(global_parameters, strict=True)
                sum_accu = 0
                num = 0
                for data, label in testDataLoader:
                    data, label = data.to(dev), label.to(dev)
                    preds = net(data)
                    preds = torch.argmax(preds, dim=1)
                    sum_accu += (preds == label).float().mean()
                    num += 1
                accuracy = (sum_accu / num).cpu().item()
                print('accuracy: {}'.format(accuracy))
                AsyAccuracy.append(accuracy)

        if (i + 1) % args['save_freq'] == 0:
            torch.save(net.state_dict(), os.path.join(args['save_path'],
                                                      '{}_num_comm{}'.format(args['model_name'], i)))
            # torch.save(net, os.path.join(args['save_path'],
            #                              '{}_num_comm{}_E{}_B{}_lr{}_num_clients{}_cf{}'.format(args['model_name'],
            #                                                                                     i, args['epoch'],
            #                                                                                     args['batchsize'],
            #                                                                                     args['learning_rate'],
            #                                                                                     args['num_of_clients'],
            #                                                                                     args['cfraction'])))
        r = os.path.join(os.getcwd(), "nonIID", "SHE", "Cifar10")
        os.makedirs(r, exist_ok=True)  # 确保目录存在
        filename = '20_1_0.4'
        np.savetxt(os.path.join(r, filename), AsyAccuracy)