"""
APPAFL-SWIM整合数据处理模块
使用APPAFL的数据集划分方式，支持IID和Non-IID数据分布
"""

import numpy as np
import torch
from torch.utils.data import TensorDataset, DataLoader
import torchvision
import torchvision.transforms as transforms
import pickle
import os


class GetDataSet(object):
    """
    数据集获取类，支持MNIST和CIFAR-10数据集
    使用APPAFL的数据划分方式
    """
    
    def __init__(self, dataSetName, isIID):
        self.name = dataSetName
        self.train_data = None
        self.train_label = None
        self.train_data_size = None
        self.test_data = None
        self.test_label = None
        self.test_data_size = None

        self._index_in_train_epoch = 0

        if self.name == 'mnist':
            self.mnistDataSetConstruct(isIID)
        elif self.name == 'cifar10':
            self.load_data(isIID)
        else:
            raise ValueError(f"不支持的数据集: {self.name}")

    def load_data(self, isIID):
        """
        加载CIFAR-10数据集
        
        Args:
            isIID: 是否为IID数据分布
        """
        # 定义数据变换
        transform_train = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
        ])
        
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
        ])

        # 下载并加载CIFAR-10数据集
        train_dataset = torchvision.datasets.CIFAR10(
            root='./data', train=True, download=True, transform=None)
        test_dataset = torchvision.datasets.CIFAR10(
            root='./data', train=False, download=True, transform=None)

        # 提取数据和标签
        train_data = np.array(train_dataset.data)
        train_labels = np.array(train_dataset.targets)
        test_data = np.array(test_dataset.data)
        test_labels = np.array(test_dataset.targets)

        # 数据预处理：转换为浮点数并归一化
        train_data = train_data.astype(np.float32) / 255.0
        test_data = test_data.astype(np.float32) / 255.0

        # 调整数据维度：从(N, H, W, C)转换为(N, C, H, W)
        train_data = np.transpose(train_data, (0, 3, 1, 2))
        test_data = np.transpose(test_data, (0, 3, 1, 2))

        self.train_data_size = len(train_data)
        self.test_data_size = len(test_data)

        print(f"CIFAR-10数据集加载完成:")
        print(f"  训练集大小: {self.train_data_size}")
        print(f"  测试集大小: {self.test_data_size}")
        print(f"  数据分布: {'IID' if isIID else 'Non-IID'}")

        # 根据IID设置进行数据划分
        if isIID:
            # IID：随机打乱数据
            order = np.arange(self.train_data_size)
            np.random.shuffle(order)
            self.train_data = train_data[order]
            self.train_label = train_labels[order]
        else:
            # Non-IID：按标签排序
            sorted_indices = np.argsort(train_labels)
            self.train_data = train_data[sorted_indices]
            self.train_label = train_labels[sorted_indices]

        self.test_data = test_data
        self.test_label = test_labels

    def mnistDataSetConstruct(self, isIID):
        """
        构建MNIST数据集（保留APPAFL原始实现的接口）
        
        Args:
            isIID: 是否为IID数据分布
        """
        # 这里可以根据需要实现MNIST数据集的加载
        # 目前主要关注CIFAR-10，所以暂时抛出异常
        raise NotImplementedError("MNIST数据集加载暂未实现，请使用CIFAR-10")


class ClientsGroup(object):
    """
    客户端组管理类
    负责将数据分配给不同的客户端，使用APPAFL的数据划分方式
    """
    
    def __init__(self, data_set_name, is_iid, num_of_clients, dev, mu=0):
        self.data_set_name = data_set_name
        self.is_iid = is_iid
        self.num_of_clients = num_of_clients
        self.dev = dev
        self.mu = mu
        self.clients_set = {}

        self.test_data_loader = None

        self.dataSetBalanceAllocation()

    def dataSetBalanceAllocation(self):
        """
        数据集平衡分配
        使用APPAFL的数据划分策略
        """
        # 获取数据集
        dataSet = GetDataSet(self.data_set_name, self.is_iid)

        # 创建测试数据加载器
        test_data = torch.tensor(dataSet.test_data)
        test_label = torch.tensor(dataSet.test_label)
        self.test_data_loader = DataLoader(
            TensorDataset(test_data, test_label), 
            batch_size=100, 
            shuffle=False
        )

        train_data = dataSet.train_data
        train_label = dataSet.train_label

        # 计算每个客户端的数据量
        shard_size = dataSet.train_data_size // self.num_of_clients // 2
        shards_id = np.random.permutation(dataSet.train_data_size // shard_size)

        print(f"数据分配信息:")
        print(f"  总训练样本数: {dataSet.train_data_size}")
        print(f"  客户端数量: {self.num_of_clients}")
        print(f"  每个分片大小: {shard_size}")
        print(f"  总分片数: {len(shards_id)}")

        # 为每个客户端分配数据
        for i in range(self.num_of_clients):
            # 每个客户端获得两个分片
            shards_id1 = shards_id[i * 2]
            shards_id2 = shards_id[i * 2 + 1]
            
            # 获取对应分片的数据索引
            data_shards1 = train_data[shards_id1 * shard_size: (shards_id1 + 1) * shard_size]
            data_shards2 = train_data[shards_id2 * shard_size: (shards_id2 + 1) * shard_size]
            label_shards1 = train_label[shards_id1 * shard_size: (shards_id1 + 1) * shard_size]
            label_shards2 = train_label[shards_id2 * shard_size: (shards_id2 + 1) * shard_size]
            
            # 合并两个分片的数据
            local_data = np.vstack((data_shards1, data_shards2))
            local_label = np.hstack((label_shards1, label_shards2))
            
            # 创建客户端对象
            someone = Client(
                TensorDataset(torch.tensor(local_data), torch.tensor(local_label)), 
                self.dev
            )
            self.clients_set['client{}'.format(i)] = someone
            
            if i < 3:  # 打印前3个客户端的数据分布信息
                unique, counts = np.unique(local_label, return_counts=True)
                print(f"  客户端{i}数据分布: {dict(zip(unique, counts))}")


class Client(object):
    """
    客户端类
    负责本地训练和模型更新
    """
    
    def __init__(self, trainDataSet, dev):
        self.train_ds = trainDataSet
        self.dev = dev
        self.train_dl = None
        self.local_parameters = None

    def localUpdate(self, localEpoch, localBatchSize, Net, lossFun, opti, global_parameters, mu):
        """
        本地更新函数（APPAFL原始版本）
        
        Args:
            localEpoch: 本地训练轮数
            localBatchSize: 本地批次大小
            Net: 神经网络模型
            lossFun: 损失函数
            opti: 优化器
            global_parameters: 全局模型参数
            mu: 正则化参数
            
        Returns:
            Net.state_dict(): 更新后的本地模型参数
        """
        # 加载全局模型参数
        Net.load_state_dict(global_parameters, strict=True)
        
        # 创建数据加载器
        self.train_dl = DataLoader(self.train_ds, batch_size=localBatchSize, shuffle=True)
        
        # 本地训练
        Net.train()
        for epoch in range(localEpoch):
            for data, label in self.train_dl:
                data, label = data.to(self.dev), label.to(self.dev)
                
                # 前向传播
                preds = Net(data)
                loss = lossFun(preds, label)
                
                # 反向传播
                loss.backward()
                opti.step()
                opti.zero_grad()

        return Net.state_dict()

    def localUpdate_swim(self, localEpoch, localBatchSize, Net, lossFun, opti,
                        global_parameters, previous_nets, mu, temperature, round_idx):
        """
        本地更新函数（SWIM版本，支持对比学习）

        Args:
            localEpoch: 本地训练轮数
            localBatchSize: 本地批次大小
            Net: 神经网络模型
            lossFun: 损失函数
            opti: 优化器
            global_parameters: 全局模型参数
            previous_nets: 历史模型列表
            mu: SWIM算法的mu参数
            temperature: 对比学习温度参数
            round_idx: 当前通信轮次

        Returns:
            Net.state_dict(): 更新后的本地模型参数
        """
        # 加载全局模型参数
        Net.load_state_dict(global_parameters, strict=True)

        # 创建数据加载器
        self.train_dl = DataLoader(self.train_ds, batch_size=localBatchSize, shuffle=True)

        # 初始化对比学习相关组件
        cos = torch.nn.CosineSimilarity(dim=-1).to(self.dev)
        C = 10  # 历史特征缓存大小
        Z_prev = [None] * C
        cnt = 0

        # 本地训练
        Net.train()
        for epoch in range(localEpoch):
            for data, label in self.train_dl:
                data, label = data.to(self.dev), label.to(self.dev)

                # 前向传播
                if hasattr(Net, 'l3'):  # SWIM模型
                    h, z, preds = Net(data)

                    # 分类损失
                    loss_ce = lossFun(preds, label)

                    # 对比学习损失（如果有历史模型）
                    loss_con = torch.tensor(0.0).to(self.dev)
                    if len(previous_nets) > 0 and round_idx > 0:
                        try:
                            # 计算正样本相似度（当前特征与自身）
                            posi = torch.exp(torch.mean(cos(z, z).reshape(-1, 1)) / temperature)

                            # 初始化负样本相似度
                            nega = torch.tensor(0.0).to(self.dev)

                            # 遍历历史网络，计算对比学习损失
                            for previous_net in previous_nets:
                                if previous_net is not None:
                                    previous_net.to(self.dev)
                                    previous_net.eval()

                                    with torch.no_grad():
                                        _, z_prev, _ = previous_net(data)  # 历史网络的投影特征

                                    # 将历史特征存储到缓存中
                                    Z_prev[cnt % C] = z_prev.detach()

                                    # 计算与历史特征的相似度
                                    for i in range(C):
                                        if Z_prev[i] is not None:
                                            t = torch.mean(cos(z, Z_prev[i]).reshape(-1, 1))
                                            if t >= 0.5:
                                                # 高相似度作为正样本
                                                posi = posi + torch.exp(t / temperature)
                                            else:
                                                # 低相似度作为负样本
                                                nega = nega + torch.exp(t / temperature)

                                    # 将历史网络移回CPU
                                    previous_net.to('cpu')
                                    cnt += 1

                            # 计算对比学习损失
                            if posi > 0 and (posi + nega) > 0:
                                loss_con = torch.mean(-torch.log(posi / (posi + nega)))

                        except Exception as e:
                            print(f"对比学习损失计算错误: {e}")
                            loss_con = torch.tensor(0.0).to(self.dev)

                    # 总损失
                    loss = loss_ce + mu * loss_con
                else:  # 普通模型
                    preds = Net(data)
                    loss = lossFun(preds, label)

                # 反向传播
                loss.backward()
                opti.step()
                opti.zero_grad()

        return Net.state_dict()


def create_data_loaders(dataset_name, is_iid, num_clients, device, mu=0):
    """
    创建数据加载器的工厂函数
    
    Args:
        dataset_name: 数据集名称
        is_iid: 是否为IID分布
        num_clients: 客户端数量
        device: 设备
        mu: 正则化参数
        
    Returns:
        ClientsGroup: 客户端组对象
    """
    return ClientsGroup(dataset_name, is_iid, num_clients, device, mu)
