"""
APPAFL-SWIM整合模型模块
整合APPAFL的基础CNN模型和SWIM的ResNet模型（包括投影版和非投影版）
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models


# ==================== APPAFL原始模型 ====================

class Mnist_2NN(nn.Module):
    """MNIST两层全连接网络"""
    def __init__(self):
        super().__init__()
        self.fc1 = nn.Linear(784, 200)
        self.fc2 = nn.Linear(200, 200)
        self.fc3 = nn.Linear(200, 10)

    def forward(self, inputs):
        tensor = F.relu(self.fc1(inputs))
        tensor = F.relu(self.fc2(tensor))
        tensor = self.fc3(tensor)
        return tensor


class Mnist_CNN(nn.Module):
    """MNIST卷积神经网络"""
    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=5, stride=1, padding=2)
        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=2, padding=0)
        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=5, stride=1, padding=2)
        self.pool2 = nn.MaxPool2d(kernel_size=2, stride=2, padding=0)
        self.fc1 = nn.Linear(7*7*64, 512)
        self.fc2 = nn.Linear(512, 10)

    def forward(self, inputs):
        tensor = inputs.view(-1, 1, 28, 28)
        tensor = F.relu(self.conv1(tensor))
        tensor = self.pool1(tensor)
        tensor = F.relu(self.conv2(tensor))
        tensor = self.pool2(tensor)
        tensor = tensor.view(-1, 7*7*64)
        tensor = F.relu(self.fc1(tensor))
        tensor = self.fc2(tensor)
        return tensor


class Cifar_CNN(nn.Module):
    """CIFAR简单CNN"""
    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv2d(3, 6, 5)
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(6, 16, 5)
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, 10)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x


class CNNCifar(nn.Module):
    """CIFAR改进CNN"""
    def __init__(self):
        super(CNNCifar, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, kernel_size=(3, 3), padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=(3, 3), padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=(3, 3), padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        self.fc1 = nn.Linear(128 * 4 * 4, 1028)
        self.fc2 = nn.Linear(1028, 10)
        self.dropout = nn.Dropout(0.3)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = self.pool(F.relu(self.conv3(x)))
        x = x.view(-1, 128 * 4 * 4)
        x = self.dropout(x)
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        return x


# ==================== SWIM ResNet模型基础组件 ====================

class BasicBlock(nn.Module):
    """ResNet基础残差块"""
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None):
        super(BasicBlock, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        if groups != 1 or base_width != 64:
            raise ValueError('BasicBlock only supports groups=1 and base_width=64')
        if dilation > 1:
            raise NotImplementedError("Dilation > 1 not supported in BasicBlock")
        
        self.conv1 = nn.Conv2d(inplanes, planes, kernel_size=3, stride=stride,
                              padding=1, bias=False)
        self.bn1 = norm_layer(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=1,
                              padding=1, bias=False)
        self.bn2 = norm_layer(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class Bottleneck(nn.Module):
    """ResNet瓶颈残差块"""
    expansion = 4

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None):
        super(Bottleneck, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        width = int(planes * (base_width / 64.)) * groups
        
        self.conv1 = nn.Conv2d(inplanes, width, kernel_size=1, bias=False)
        self.bn1 = norm_layer(width)
        self.conv2 = nn.Conv2d(width, width, kernel_size=3, stride=stride,
                              padding=dilation, groups=groups, bias=False, dilation=dilation)
        self.bn2 = norm_layer(width)
        self.conv3 = nn.Conv2d(width, planes * self.expansion, kernel_size=1, bias=False)
        self.bn3 = norm_layer(planes * self.expansion)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class ResNetCifar(nn.Module):
    """适配CIFAR数据集的ResNet模型"""

    def __init__(self, block, layers, num_classes=10, zero_init_residual=False,
                 groups=1, width_per_group=64, replace_stride_with_dilation=None,
                 norm_layer=None):
        super(ResNetCifar, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        self._norm_layer = norm_layer

        self.inplanes = 64
        self.dilation = 1
        if replace_stride_with_dilation is None:
            replace_stride_with_dilation = [False, False, False]
        if len(replace_stride_with_dilation) != 3:
            raise ValueError("replace_stride_with_dilation should be None "
                           "or a 3-element tuple, got {}".format(replace_stride_with_dilation))
        self.groups = groups
        self.base_width = width_per_group
        
        # 适配CIFAR的32x32图像，使用3x3卷积，步长1
        self.conv1 = nn.Conv2d(3, self.inplanes, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn1 = norm_layer(self.inplanes)
        self.relu = nn.ReLU(inplace=True)
        
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2,
                                     dilate=replace_stride_with_dilation[0])
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2,
                                     dilate=replace_stride_with_dilation[1])
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2,
                                     dilate=replace_stride_with_dilation[2])
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * block.expansion, num_classes)

        # 权重初始化
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

        if zero_init_residual:
            for m in self.modules():
                if isinstance(m, Bottleneck):
                    nn.init.constant_(m.bn3.weight, 0)
                elif isinstance(m, BasicBlock):
                    nn.init.constant_(m.bn2.weight, 0)

    def _make_layer(self, block, planes, blocks, stride=1, dilate=False):
        norm_layer = self._norm_layer
        downsample = None
        previous_dilation = self.dilation
        if dilate:
            self.dilation *= stride
            stride = 1
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv2d(self.inplanes, planes * block.expansion, kernel_size=1, 
                         stride=stride, bias=False),
                norm_layer(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample, self.groups,
                          self.base_width, previous_dilation, norm_layer))
        self.inplanes = planes * block.expansion
        for _ in range(1, blocks):
            layers.append(block(self.inplanes, planes, groups=self.groups,
                              base_width=self.base_width, dilation=self.dilation,
                              norm_layer=norm_layer))

        return nn.Sequential(*layers)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)

        return x


def ResNet18_cifar(num_classes=10, **kwargs):
    """创建适配CIFAR数据集的ResNet-18模型"""
    return ResNetCifar(BasicBlock, [2, 2, 2, 2], num_classes=num_classes, **kwargs)


def ResNet50_cifar(num_classes=10, **kwargs):
    """创建适配CIFAR数据集的ResNet-50模型"""
    return ResNetCifar(Bottleneck, [3, 4, 6, 3], num_classes=num_classes, **kwargs)


# ==================== SWIM联邦对比学习模型 ====================

class ModelFedCon(nn.Module):
    """
    联邦对比学习模型（带投影头）
    整合特征提取、投影层和分类层，支持对比学习
    """

    def __init__(self, base_model, out_dim, n_classes, net_configs=None):
        """
        初始化联邦对比学习模型

        Args:
            base_model: 基础模型名称 ('resnet18', 'resnet50', 'cnn')
            out_dim: 投影层输出维度
            n_classes: 分类数量
            net_configs: 网络配置参数
        """
        super(ModelFedCon, self).__init__()

        # 根据基础模型类型创建特征提取器
        if base_model == "resnet18":
            basemodel = ResNet18_cifar(num_classes=n_classes)
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = 512  # ResNet18的特征维度
        elif base_model == "resnet50":
            basemodel = ResNet50_cifar(num_classes=n_classes)
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = 2048  # ResNet50的特征维度
        elif base_model == "cnn":
            # 使用APPAFL的CNN作为基础模型
            self.features = CNNCifar()
            # 移除最后的分类层
            self.features.fc2 = nn.Identity()
            num_ftrs = 1028
        else:
            raise ValueError(f"Unsupported base model: {base_model}")

        # 投影MLP（用于对比学习）
        self.l1 = nn.Linear(num_ftrs, num_ftrs)  # 第一个投影层
        self.l2 = nn.Linear(num_ftrs, out_dim)   # 第二个投影层

        # 分类层
        self.l3 = nn.Linear(out_dim, n_classes)

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入张量

        Returns:
            h: 特征表示（用于对比学习）
            x: 投影特征（用于对比学习）
            y: 分类输出
        """
        # 特征提取
        h = self.features(x)
        h = h.squeeze()  # 移除多余的维度

        # 投影层（用于对比学习）
        x = self.l1(h)      # 第一个投影层
        x = F.relu(x)       # ReLU激活
        x = self.l2(x)      # 第二个投影层

        # 分类层
        y = self.l3(x)

        return h, x, y  # 返回特征、投影特征和分类结果


class ModelFedCon_noheader(nn.Module):
    """
    联邦对比学习模型（不带投影头）
    这是简化版本的联邦学习模型，直接从特征到分类，不使用投影头
    """

    def __init__(self, base_model, out_dim, n_classes, net_configs=None):
        """
        初始化联邦对比学习模型（无投影头版本）

        Args:
            base_model: 基础模型名称 ('resnet18', 'resnet50', 'cnn')
            out_dim: 输出维度（此版本中未使用）
            n_classes: 分类数量
            net_configs: 网络配置参数
        """
        super(ModelFedCon_noheader, self).__init__()

        # 根据基础模型类型创建特征提取器
        if base_model == "resnet18":
            basemodel = ResNet18_cifar(num_classes=n_classes)
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = 512  # ResNet18的特征维度
        elif base_model == "resnet50":
            basemodel = ResNet50_cifar(num_classes=n_classes)
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = 2048  # ResNet50的特征维度
        elif base_model == "cnn":
            # 使用APPAFL的CNN作为基础模型
            self.features = CNNCifar()
            # 移除最后的分类层
            self.features.fc2 = nn.Identity()
            num_ftrs = 1028
        else:
            raise ValueError(f"Unsupported base model: {base_model}")

        # 注意：此版本不使用投影头，直接从特征到分类
        # 分类层（直接从特征映射到类别）
        self.l3 = nn.Linear(num_ftrs, n_classes)

    def forward(self, x):
        """
        前向传播（无投影头版本）

        Args:
            x: 输入张量

        Returns:
            h: 特征表示
            h: 特征表示（重复返回，保持接口一致性）
            y: 分类输出
        """
        # 特征提取
        h = self.features(x)
        h = h.squeeze()  # 移除多余的维度

        # 直接分类（不使用投影头）
        y = self.l3(h)

        # 返回特征、特征（重复）和分类结果
        # 注意：返回h两次是为了与ModelFedCon保持接口一致性
        return h, h, y


# ==================== 模型创建工厂函数 ====================

def create_model(model_type, base_model="resnet18", use_project_head=True, out_dim=256, n_classes=10):
    """
    模型创建工厂函数

    Args:
        model_type: 模型类型 ('appafl', 'swim')
        base_model: 基础模型 ('resnet18', 'resnet50', 'cnn')
        use_project_head: 是否使用投影头（仅对SWIM有效）
        out_dim: 投影层输出维度
        n_classes: 分类数量

    Returns:
        model: 创建的模型实例
    """
    if model_type == "appafl":
        if base_model == "cnn":
            return CNNCifar()
        elif base_model == "simple_cnn":
            return Cifar_CNN()
        else:
            raise ValueError(f"APPAFL不支持的基础模型: {base_model}")

    elif model_type == "swim":
        if use_project_head:
            return ModelFedCon(base_model, out_dim, n_classes)
        else:
            return ModelFedCon_noheader(base_model, out_dim, n_classes)

    else:
        raise ValueError(f"不支持的模型类型: {model_type}")
